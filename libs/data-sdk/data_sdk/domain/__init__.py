from enum import Enum
from typing import Any

from pydantic import BaseModel, GetCoreSchemaHandler
from pydantic_core import CoreSchema, core_schema


class BaseIntField(int):
    @classmethod
    def __get_pydantic_core_schema__(
        cls, source_type: Any, handler: GetCoreSchemaHandler
    ) -> CoreSchema:
        return core_schema.no_info_after_validator_function(cls, handler(int))


class BaseStrField(str):
    @classmethod
    def __get_pydantic_core_schema__(
        cls, source_type: Any, handler: GetCoreSchemaHandler
    ) -> CoreSchema:
        return core_schema.no_info_after_validator_function(cls, handler(str))


class Portal(str, Enum):
    APPLE = "apple"
    EPIC = "epic"
    GOG = "gog"
    GOOGLE = "google"
    HUMBLE = "humble"
    META = "meta"
    MICROSOFT = "microsoft"
    NINTENDO = "nintendo"
    PLAYSTATION = "playstation"
    STEAM = "steam"


ALL_PORTALS: list[Portal] = [portal for portal in Portal]


class DisplayPortal(str, Enum):
    APPLE = "Apple"
    EPIC = "Epic"
    GOG = "GOG"
    GOOGLE = "Google"
    HUMBLE = "Humble"
    META = "Meta"
    MICROSOFT = "Microsoft"
    NINTENDO = "Nintendo"
    PLAYSTATION = "PlayStation"
    STEAM = "Steam"


class SaleCategory(str, Enum):
    FREE = "Free"
    FREE_AND_RETURN = "Free & Return"
    FREE_AND_SALE = "Free & Sale"
    FREE_INVALID = "Invalid Free"
    RETAIL = "Retail"
    NON_BILLABLE = "Non-billable"
    SALE_ADJUSTMENT = "Sale Adjustment"
    BLANK_RECORD = "Blank Record"
    RETURN = "Return"
    RETURN_INVALID = "Invalid Return"
    SALE = "Sale"
    SALE_INVALID = "Invalid Sale"


class TableNameType(BaseStrField):
    pass


class TableVersion(BaseStrField):
    pass


class TableName(str, Enum):
    EXTERNAL_REPORTS = "external_reports"
    EXTERNAL_SKUS = "external_skus"
    EXTERNAL_STUDIOS = "external_studios"
    EXTERNAL_SHARED = "external_shared"
    EXTERNAL_COUNTRY_CODES = "external_country_codes"
    EXTERNAL_STEAM_EVENTS = "external_steam_events"
    EXTERNAL_CURRENCY_EXCHANGE_RATES = "external_currency_exchange_rates"
    EXTERNAL_FEATURE_FLAGS = "external_feature_flags"

    OBSERVATION_SALES = "observation_sales"
    OBSERVATION_DISCOUNTS = "observation_discounts"
    OBSERVATION_VISIBILITY = "observation_visibility"
    OBSERVATION_WISHLIST_ACTIONS = "observation_wishlist_actions"
    OBSERVATION_WISHLIST_COHORTS = "observation_wishlist_cohorts"
    OBSERVATION_CUMULATIVE_WISHLIST_SALES = "observation_cumulative_wishlist_sales"
    OBSERVATION_WISHLIST_BALANCE = "observation_wishlist_balance"

    SILVER_ACQUISITION_PROPERTIES = "silver_acquisition_properties"
    SILVER_TRAFFIC_SOURCE = "silver_traffic_source"
    SILVER_SKUS = "silver_skus"
    SILVER_PORTALS = "silver_portals"
    SILVER_REPORTS = "silver_reports"

    # LEGACY TABLES
    DIM_ACQUISITION_PROPERTIES = "dim_acquisition_properties"
    DIM_PORTALS = "dim_portals"
    DIM_SKU = "dim_sku"
    DIM_SOURCE_FILE = "dim_source_file"
    DIM_STUDIO = "dim_studio"
    DIM_TRAFFIC_SOURCE = "dim_traffic_source"
    DIM_SHARED = "dim_shared"

    FACT_BASELINE = "fact_baseline"
    FACT_DATES_COVERAGE = "fact_dates_coverage"
    FACT_DETECTED_EVENTS = "fact_detected_events"
    FACT_DISCOUNT_DEPTH_INPUT = "fact_discount_depth_input"
    FACT_DISCOUNTS = "fact_discounts"
    FACT_EVENT_DAY = "fact_event_day"
    FACT_SALES = "fact_sales"
    FACT_VISIBILITY = "fact_visibility"
    FACT_WISHLIST_ACTIONS = "fact_wishlist_actions"
    FACT_WISHLIST_COHORTS = "fact_wishlist_cohorts"

    SKU_EVENTS_PLANNER = "sku_events_planner"
    # TODO Get rid of this table
    SHARED = "shared"


class TableMetadata(BaseModel):
    table_name: str


class Navigation(str, Enum):
    DIRECT = "Direct Navigation"
    IMPRESSIONS = "Visits from Impressions"


def get_display_portal_from_portal(portal: Portal) -> DisplayPortal:
    return {
        Portal.STEAM: DisplayPortal.STEAM,
        Portal.NINTENDO: DisplayPortal.NINTENDO,
        Portal.PLAYSTATION: DisplayPortal.PLAYSTATION,
        Portal.MICROSOFT: DisplayPortal.MICROSOFT,
        Portal.HUMBLE: DisplayPortal.HUMBLE,
        Portal.GOG: DisplayPortal.GOG,
        Portal.META: DisplayPortal.META,
        Portal.EPIC: DisplayPortal.EPIC,
        Portal.APPLE: DisplayPortal.APPLE,
        Portal.GOOGLE: DisplayPortal.GOOGLE,
    }[portal]
